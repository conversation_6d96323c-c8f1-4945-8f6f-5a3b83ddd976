// Expense related types
export enum EXPENSE_TYPE {
  INCOME = "INCOME",
  EXPENSE = "EXPENSE",
  DEBT_BOUGHT = "DEBT_BOUGHT",
  DEBT_GIVEN = "DEBT_GIVEN",
  INVESTMENT = "INVESTMENT",
  INCOME_TAX = "INCOME_TAX",
}

export const EXPENSE_MAP = {
  INCOME: "Income",
  EXPENSE: "Expenses",
  DEBT_BOUGHT: "Debt Bought",
  DEBT_GIVEN: "Debt Given",
};

export interface Expense {
  _id?: string;
  type: EXPENSE_TYPE;
  name: string;
  note?: string;
  amount: number;
  category: string;
  eventDate?: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface ExpenseStats {
  groupedExpense: Record<EXPENSE_TYPE, number>;
  expensesByCategory: {
    name: string;
    value: number;
  }[];
  monthlyData: {
    name: string;
    income: number;
    expenses: number;
  }[];
  recentTransactions: Expense[];
}

export interface ExpenseFilters {
  type?: string;
  categories?: string[];
  dateRange?: {
    from?: Date;
    to?: Date;
  };
  searchQuery?: string;
  sortBy?: string;
  sortDirection?: "asc" | "desc";
  ignoreDate?: boolean;
}
