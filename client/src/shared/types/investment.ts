// Investment related types
export enum INVESTMENT_TYPE {
  STOCK = "STOCK",
  MUTUAL_FUND = "MUTUAL_FUND",
  GOLD = "GOLD",
  SILVER = "SILVER",
  CURRENCY = "CURRENCY",
}

export enum INVESTMENT_PURPOSE {
  MONITORING = "MONITORING",
  OWNED = "OWNED",
}

export interface Purchase {
  price: number;
  quantity: number;
}

export interface Investment {
  _id?: string;
  name: string;
  symbol: string;
  // Legacy fields kept for backward compatibility
  purchasedPrice?: number;
  quantity?: number;
  // New field for multiple purchases
  purchases?: Purchase[];
  type: INVESTMENT_TYPE;
  purpose: INVESTMENT_PURPOSE;
  targetPrice?: number;
  userId?: string;
  createdAt?: Date;
  updatedAt?: Date;
  // New fields for owned stocks
  currentPrice?: number;
  profitPerShare?: number;
  totalProfit?: number;
  // Calculated fields from purchases array
  avgPurchasePrice?: number;
  totalQuantity?: number;
}

export interface InvestmentStats {
  totalInvestment: number;
  totalCurrentValue: number;
  totalReturn: number;
  returnPercentage: number;
  investmentsByType: {
    name: string;
    value: number;
  }[];
  recentInvestments: Investment[];
  performanceData: {
    name: string;
    value: number;
  }[];
}

export interface InvestmentFilters {
  type?: INVESTMENT_TYPE;
  purpose?: INVESTMENT_PURPOSE;
  searchQuery?: string;
  sortBy?: string;
  sortDirection?: "asc" | "desc";
}

export interface StockDataType {
  name: string;
  data: [number, number][];
  purpose?: INVESTMENT_PURPOSE;
}
