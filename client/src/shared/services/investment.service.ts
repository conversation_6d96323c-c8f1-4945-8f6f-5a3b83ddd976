import api from "./api";
import type {
  Investment,
  InvestmentStats,
  InvestmentFilters,
  StockDataType,
  INVESTMENT_TYPE,
  INVESTMENT_PURPOSE,
} from "../types/investment";

// Get all investments
export const getInvestments = async (
  filters?: InvestmentFilters
): Promise<Investment[]> => {
  let queryParams = "";

  if (filters) {
    const params = new URLSearchParams();

    if (filters.type) {
      params.append("type", filters.type);
    }

    if (filters.purpose) {
      params.append("purpose", filters.purpose);
    }

    if (filters.searchQuery) {
      params.append("search", filters.searchQuery);
    }

    if (filters.sortBy) {
      params.append("sortBy", filters.sortBy);
      params.append("sortDirection", filters.sortDirection || "desc");
    }

    queryParams = `?${params.toString()}`;
  }

  const response = await api.get(`/investments${queryParams}`);
  return response.data.data;
};

// Get investment by ID
export const getInvestmentById = async (id: string): Promise<Investment> => {
  const response = await api.get(`/investments/${id}`);
  return response.data.data;
};

// Create new investment
export const createInvestment = async (
  investment: Investment
): Promise<Investment> => {
  const response = await api.post("/investments", investment);
  return response.data.data;
};

// Update investment
export const updateInvestment = async (
  id: string,
  investment: Investment
): Promise<Investment> => {
  const response = await api.put(`/investments/${id}`, investment);
  return response.data.data;
};

// Delete investment
export const deleteInvestment = async (id: string): Promise<void> => {
  await api.delete(`/investments/${id}`);
};

// Get investment statistics for dashboard
export const getInvestmentStats = async (): Promise<InvestmentStats> => {
  const response = await api.get("/investments/stats");
  return response.data.data;
};

export interface StockDataType {
  name: string;
  data: [number, number][];
  purpose?: INVESTMENT_PURPOSE;
}
// Get price tracking data
export const getPriceTracking = async (
  type: string
): Promise<StockDataType[]> => {
  const response = await api.get(`/investments/price-tracking?type=${type}`);
  return response.data;
};
