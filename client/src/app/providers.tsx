import { ReactNode } from "react";
import { AuthProvider } from "@/features/auth/contexts/AuthContext";
import { ExpenseProvider } from "@/features/transactions/contexts/ExpenseContext";
import { InvestmentProvider } from "@/features/investments/contexts/InvestmentContext";
import { ThemeProvider } from "@/shared/components/layout";

interface AppProvidersProps {
  children: ReactNode;
}

export function AppProviders({ children }: AppProvidersProps) {
  return (
    <ThemeProvider>
      <AuthProvider>
        <ExpenseProvider>
          <InvestmentProvider>
            {children}
          </InvestmentProvider>
        </ExpenseProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}
