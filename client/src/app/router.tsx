import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { ProtectedRoute } from "@/features/auth/components/ProtectedRoute";
import { Layout } from "@/shared/components/layout";
import { ROUTES } from "@/shared/constants";

// Page imports
import HomePage from "@/pages/HomePage";
import DashboardPage from "@/pages/DashboardPage";
import InvestmentPage from "@/pages/InvestmentPage";
import SignInPage from "@/pages/SignInPage";
import SignUpPage from "@/pages/SignUpPage";
import ForgotPasswordPage from "@/pages/ForgotPasswordPage";

export function AppRouter() {
  return (
    <Router>
      <div className="relative">
        <Routes>
          <Route path={ROUTES.HOME} element={<HomePage />} />
          <Route path={ROUTES.SIGNIN} element={<SignInPage />} />
          <Route path={ROUTES.SIGNUP} element={<SignUpPage />} />
          <Route path={ROUTES.FORGOT_PASSWORD} element={<ForgotPasswordPage />} />
          <Route
            path={ROUTES.DASHBOARD}
            element={
              <ProtectedRoute>
                <Layout>
                  <DashboardPage />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path={ROUTES.INVESTMENT}
            element={
              <ProtectedRoute>
                <Layout>
                  <InvestmentPage />
                </Layout>
              </ProtectedRoute>
            }
          />
        </Routes>
        <div id="toast-container" className="fixed top-4 right-4 z-50"></div>
      </div>
    </Router>
  );
}
