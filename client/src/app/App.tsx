import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import SignInForm from "./components/auth/SignInForm";
import SignUpForm from "@/components/auth/SignUpForm";
import ForgotPasswordForm from "@/components/auth/ForgotPasswordForm";
import DashboardPage from "@/Pages/Dashboard";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";
import { Home } from "@/components/Home";
import Layout from "./components/layout";
import InvestmentPage from "./Pages/Investment";

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="relative">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/signin" element={<SignInForm />} />
            <Route path="/signup" element={<SignUpForm />} />
            <Route path="/forgot-password" element={<ForgotPasswordForm />} />
            <Route
              path="/dashboard"
              element={
                <ProtectedRoute>
                  <Layout>
                    <DashboardPage />
                  </Layout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/investment"
              element={
                <ProtectedRoute>
                  <Layout>
                    <InvestmentPage />
                  </Layout>
                </ProtectedRoute>
              }
            />
          </Routes>
          <div id="toast-container" className="fixed top-4 right-4 z-50"></div>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
