import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import {
  getInvestmentStats,
  InvestmentStats,
} from "@/services/investment.service";

interface InvestmentContextType {
  investmentStats: InvestmentStats | null;
  loading: boolean;
  error: boolean;
  refreshData: () => void;
  refreshTrigger: number;
}

const InvestmentContext = createContext<InvestmentContextType | undefined>(
  undefined
);

export function InvestmentProvider({ children }: { children: ReactNode }) {
  const [investmentStats, setInvestmentStats] =
    useState<InvestmentStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const refreshData = () => {
    setRefreshTrigger((prev) => prev + 1);
  };

  useEffect(() => {
    const fetchInvestmentStats = async () => {
      try {
        setLoading(true);
        const stats = await getInvestmentStats();
        setInvestmentStats(stats);
        setError(false);
      } catch (err) {
        console.error("Failed to fetch investment statistics:", err);
        setError(true);
      } finally {
        setLoading(false);
      }
    };

    fetchInvestmentStats();
  }, [refreshTrigger]);

  return (
    <InvestmentContext.Provider
      value={{ investmentStats, loading, error, refreshData, refreshTrigger }}
    >
      {children}
    </InvestmentContext.Provider>
  );
}

export function useInvestment() {
  const context = useContext(InvestmentContext);
  if (context === undefined) {
    throw new Error("useInvestment must be used within an InvestmentProvider");
  }
  return context;
}
