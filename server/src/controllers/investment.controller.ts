import axios from 'axios';
import { load as cheerio } from 'cheerio';
import { Request, Response } from 'express';
import { User } from '../models/user.model';
import { Investments } from '../models/investment.model';
import { convertDateFormat, convertToStockFormat } from '../utils/misc';

interface MutualFundMap {
  [key: string]: number;
}

interface StockMap {
  [key: string]: string;
}

export interface StockData {
  name: string;
  data: [number, number][];
}

interface MutualFundResponse {
  folio: {
    name: string;
    data: [number, number][];
  };
}

export interface StockResponse {
  candles: [number, number][];
}

const MUTUALFUNDS: MutualFundMap = {
  QUANT_FLEXI: 120843,
  NIPPON_SMALL: 118778,
};

const STOCKS: StockMap = {
  KARNATAKA_BANK_177_30: 'KTKBANK',
  MOIL_367_35: 'MOIL',
  PVRINOX_1328_2: 'PVRINOX',
  NBCC_89: 'NBCC',
  SOUTHBANK_26_80: 'SOUTHBANK',
  SUZLON_wait_55: 'SUZLON',
  VEDL_WAIT_400to405: 'VEDL',
};

const buildMutualFundUrl = (id: number): string =>
  `https://groww.in/v1/api/data/mf/web/v1/scheme/${id}/graph?benchmark=false&months=1`;

const buildStockUrl = (id: string): string =>
  `https://groww.in/v1/api/charting_service/v2/chart/delayed/exchange/NSE/segment/CASH/${id}/monthly/v2?intervalInMinutes=30&minimal=true`;

const formatMutualFundData = (_: string, data: MutualFundResponse): StockData => ({
  name: data.folio.name,
  data: data.folio.data,
});

const formatStockData = (name: string, data: StockResponse): StockData => ({
  name,
  data: data?.candles.map((k) => [k[0] * 1000, k[1]]) || [],
});

interface GoldRate {
  date: string;
  gold22KT: number;
}

const fetchGoldRates = async (): Promise<StockData[]> => {
  const { data } = await axios.get('https://www.tanishq.co.in/gold-rate.html?lang=en_IN');

  const $ = cheerio(data);

  const getArray = (selector: string): string[] => {
    const rawValue = $(selector).attr('value') || '';
    return rawValue
      .replaceAll('[', '')
      .replaceAll(']', '')
      .split(',')
      .map((k) => k.trim());
  };

  const dates = getArray('#goldRateDates');
  // const rates18 = getArray("#goldRate18KT");
  const rates22 = getArray('#goldRate22KT');
  // const rates24 = getArray('#goldRate24KT');

  const sliced = dates
    .map((date, index) => ({
      date: convertDateFormat(date),
      // gold18KT: parseFloat(rates18[index]),
      gold22KT: parseFloat(rates22[index]),
      // gold24KT: parseFloat(rates24[index]),
    }))
    .slice(10);

  return [convertToStockFormat('Gold 22KT', 'gold22KT', sliced)];
};

interface SilverRate {
  date: string;
  silver1g: number;
}

const fetchSilverRates = async (): Promise<StockData[]> => {
  const { data } = await axios.get(
    'https://www.thehindubusinessline.com/silver-rate-today/Chennai/',
    {
      headers: {
        'User-Agent':
          'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        accept:
          'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-encoding': 'gzip, deflate, br, zstd',
        'accept-language': 'en-US,en;q=0.9,ta-IN;q=0.8,ta;q=0.7',
        'cache-control': 'no-cache',
        dnt: '1',
        pragma: 'no-cache',
        priority: 'u=0, i',
        'sec-ch-ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Linux"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'none',
        'sec-fetch-user': '?1',
        'upgrade-insecure-requests': '1',
      },
    },
  );
  const $ = cheerio(data);
  // Select the third table with the class "table table-balance-sheet"
  const table = $('.table.table-balance-sheet').eq(2);
  const rows = table.find('tbody tr');

  const result: SilverRate[] = [];

  rows.each((i, row) => {
    const cols = $(row).find('td');
    if (cols.length >= 4) {
      // console.log(
      //   parseInt($(cols[1]).text().trim().trim().replace('₹', '').replaceAll(',', '')) / 10,
      // );
      result.push({
        date: $(cols[0]).text().trim(),
        silver1g: parseInt($(cols[1]).text().trim().replace('₹', '').replaceAll(',', '')) / 10,
        // silver100g: $(cols[2]).text().trim(),
        // silver1kg: $(cols[3]).clone().children().remove().end().text().trim(), // remove span and just get price
      });
    }
  });

  return [convertToStockFormat('Silver', 'silver1g', result)];
};

interface CurrencyRateItem {
  time: number;
  value: number;
  source: string;
  target: string;
}

interface CurrencyRate {
  date: string;
  usdToInr: string;
  inrToUsd: number;
  source: string;
  target: string;
}

const fetchCurrencyRates = async (): Promise<StockData[]> => {
  try {
    const { data } = await axios.get<CurrencyRateItem[]>(
      'https://wise.com/rates/history+live?source=INR&target=USD&length=30&resolution=daily&unit=day',
      {
        headers: {
          'User-Agent':
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        },
      },
    );

    return [
      convertToStockFormat(
        'USD',
        'usdToInr',
        data.map((item) => {
          const date = new Date(item.time).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
          });

          // Format the flipped rate to show 1 USD = x INR with 2 decimal places
          const flippedRate = item.value > 0 ? (1 / item.value).toFixed(2) : '0';

          return {
            date,
            usdToInr: flippedRate,
            inrToUsd: item.value,
            source: item.source,
            target: item.target,
          };
        }),
      ),
    ];
  } catch (error) {
    console.error('Error fetching currency rates:', error);
    return [];
  }
};

interface ScraperStrategy {
  isScraper: true;
  fetchData: () => Promise<any>;
}

interface DataStrategy<T, R> {
  isScraper: false;
  items: T;
  buildUrl: (id: any) => string;
  formatData: (name: string, data: any) => R;
}

type Strategy = ScraperStrategy | DataStrategy<MutualFundMap | StockMap, StockData>;

interface StrategyMap {
  [key: string]: Strategy;
}

const typeStrategy: StrategyMap = {
  mutual_fund: {
    // items: MUTUALFUNDS,
    buildUrl: buildMutualFundUrl,
    formatData: formatMutualFundData,
    isScraper: false,
  } as DataStrategy<MutualFundMap, StockData>,
  stock: {
    // items: STOCKS, // Use the predefined STOCKS object for testing, but we'll override with database values
    buildUrl: buildStockUrl,
    formatData: formatStockData,
    isScraper: false,
  } as DataStrategy<StockMap, StockData>,
  gold: {
    isScraper: true,
    fetchData: fetchGoldRates,
  },
  silver: {
    isScraper: true,
    fetchData: fetchSilverRates,
  },
  currency: {
    isScraper: true,
    fetchData: fetchCurrencyRates,
  },
};

const fetchStockData = async <T, R>(
  stockItems: T,
  buildUrl: (id: any) => string,
  formatData: (name: string, data: any) => R,
): Promise<R[]> => {
  try {
    // console.log('Fetching stock data for:', stockItems);

    const responses = await Promise.all(
      Object.entries(stockItems as Record<string, any>).map(async ([name, id]) => {
        const { data } = await axios.get(buildUrl(id));
        return formatData(name, data);
      }),
    );
    return responses;
  } catch (error) {
    console.error('Error fetching stock data:', error);
    return [];
  }
};

export const getInvestments = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { type, purpose, search, sortBy, sortDirection } = req.query;

    const filter: any = { userId: req.user?.id };

    if (type) {
      filter.type = type;
    }

    if (purpose) {
      filter.purpose = purpose;
    }

    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { symbol: { $regex: search, $options: 'i' } },
      ];
    }

    // Build sort object
    const sort: any = {};
    if (sortBy) {
      sort[sortBy as string] = sortDirection === 'desc' ? -1 : 1;
    } else {
      sort.createdAt = -1; // Default sort by creation date, newest first
    }

    const investments = await Investments.find(filter).sort(sort);

    return res.status(200).json({
      success: true,
      count: investments.length,
      data: investments,
    });
  } catch (error) {
    console.error('Error fetching investments:', error);
    return res.status(500).json({
      success: false,
      error: 'Server Error',
    });
  }
};

// Get single investment
export const getInvestment = async (req: Request, res: Response): Promise<Response> => {
  try {
    const investment = await Investments.findOne({
      _id: req.params.id,
      userId: req.user?.id,
    });

    if (!investment) {
      return res.status(404).json({
        success: false,
        error: 'Investment not found',
      });
    }

    return res.status(200).json({
      success: true,
      data: investment,
    });
  } catch (error) {
    console.error('Error fetching investment:', error);
    return res.status(500).json({
      success: false,
      error: 'Server Error',
    });
  }
};

export const createInvestment = async (req: Request, res: Response): Promise<Response> => {
  try {
    // Add user ID to request body
    req.body.userId = req.user?.id;

    const investment = await Investments.create(req.body);

    return res.status(201).json({
      success: true,
      data: investment,
    });
  } catch (error) {
    console.error('Error creating investment:', error);

    if ((error as any).name === 'ValidationError') {
      const messages = Object.values((error as any).errors).map((val: any) => val.message);
      return res.status(400).json({
        success: false,
        error: messages,
      });
    }

    return res.status(500).json({
      success: false,
      error: 'Server Error',
    });
  }
};

// Update investment
export const updateInvestment = async (req: Request, res: Response): Promise<Response> => {
  try {
    let investment = await Investments.findOne({
      _id: req.params.id,
      userId: req.user?.id,
    });

    if (!investment) {
      return res.status(404).json({
        success: false,
        error: 'Investment not found',
      });
    }

    investment = await Investments.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true,
    });

    return res.status(200).json({
      success: true,
      data: investment,
    });
  } catch (error) {
    console.error('Error updating investment:', error);

    if ((error as any).name === 'ValidationError') {
      const messages = Object.values((error as any).errors).map((val: any) => val.message);
      return res.status(400).json({
        success: false,
        error: messages,
      });
    }

    return res.status(500).json({
      success: false,
      error: 'Server Error',
    });
  }
};

// Delete investment
export const deleteInvestment = async (req: Request, res: Response): Promise<Response> => {
  try {
    const investment = await Investments.findOne({
      _id: req.params.id,
      userId: req.user?.id,
    });

    if (!investment) {
      return res.status(404).json({
        success: false,
        error: 'Investment not found',
      });
    }

    await investment.deleteOne();

    return res.status(200).json({
      success: true,
      data: {},
    });
  } catch (error) {
    console.error('Error deleting investment:', error);
    return res.status(500).json({
      success: false,
      error: 'Server Error',
    });
  }
};

export const getInvestmentStats = async (req: Request, res: Response): Promise<Response> => {
  try {
    const allInvestments = await Investments.find({ userId: req.user.id });

    const ownedStocks = allInvestments.filter(
      (investment) => investment.purpose === 'OWNED' && investment.type === 'STOCK',
    );

    const stockSymbols: Record<string, string> = {};
    ownedStocks.forEach((stock) => {
      stockSymbols[stock.name] = stock.symbol;
    });

    const stockStrategy = typeStrategy.stock as DataStrategy<any, StockData>;
    const { buildUrl, formatData } = stockStrategy;

    const stockResponses = await fetchStockData(stockSymbols, buildUrl, formatData);

    const currentPrices: Record<string, number> = {};
    stockResponses.forEach((stockData) => {
      // Get the latest price from the data array (last element)
      if (stockData.data && stockData.data.length > 0) {
        const latestDataPoint = stockData.data[stockData.data.length - 1];
        currentPrices[stockData.name] = latestDataPoint[1];
      }
    });

    // Calculate investment statistics
    let totalInvestment = 0;
    let totalCurrentValue = 0;

    // Process each owned stock and add current price and profit information
    const enhancedInvestments = allInvestments.map((investment) => {
      // Create a copy of the investment object that we can modify
      const enhancedInvestment = investment.toObject();

      // For owned stocks, add current price and profit information
      if (investment.purpose === 'OWNED' && investment.type === 'STOCK') {
        // Check if we have purchases array or legacy fields
        const purchases =
          investment.purchases && investment.purchases.length > 0
            ? investment.purchases
            : investment.purchasedPrice && investment.quantity
            ? [{ price: investment.purchasedPrice, quantity: investment.quantity }]
            : [];

        if (purchases.length > 0) {
          let investmentValue = 0;
          let totalQuantity = 0;

          // Calculate total investment value and quantity from all purchases
          purchases.forEach((purchase: { price: number; quantity: number }) => {
            investmentValue += purchase.price * purchase.quantity;
            totalQuantity += purchase.quantity;
          });

          // Calculate average purchase price
          const avgPurchasePrice = investmentValue / totalQuantity;

          // Add current price if available
          if (currentPrices[investment.name]) {
            const currentPrice = currentPrices[investment.name];
            enhancedInvestment.currentPrice = currentPrice;

            // Calculate profit/loss per share based on average purchase price
            const profitPerShare = currentPrice - avgPurchasePrice;
            enhancedInvestment.profitPerShare = profitPerShare;

            // Calculate total profit/loss
            const currentValue = currentPrice * totalQuantity;
            // Profit = Current Value - Investment Value
            const totalProfit = currentValue - investmentValue;
            enhancedInvestment.totalProfit = totalProfit;

            totalCurrentValue += currentValue;
          } else {
            // If we don't have current price, use the average purchase price as fallback
            enhancedInvestment.currentPrice = avgPurchasePrice;
            enhancedInvestment.profitPerShare = 0;
            enhancedInvestment.totalProfit = 0;
            totalCurrentValue += investmentValue;
          }

          // Add total investment value
          totalInvestment += investmentValue;

          // Add calculated values to the enhanced investment
          enhancedInvestment.avgPurchasePrice = avgPurchasePrice;
          enhancedInvestment.totalQuantity = totalQuantity;
        }
      } else if (investment.purpose === 'OWNED') {
        // For non-stock investments that are owned
        // Check if we have purchases array or legacy fields
        const purchases =
          investment.purchases && investment.purchases.length > 0
            ? investment.purchases
            : investment.purchasedPrice && investment.quantity
            ? [{ price: investment.purchasedPrice, quantity: investment.quantity }]
            : [];

        if (purchases.length > 0) {
          let investmentValue = 0;
          let totalQuantity = 0;

          // Calculate total investment value and quantity from all purchases
          purchases.forEach((purchase: { price: number; quantity: number }) => {
            investmentValue += purchase.price * purchase.quantity;
            totalQuantity += purchase.quantity;
          });

          totalInvestment += investmentValue;
          totalCurrentValue += investmentValue;

          // Add calculated values to the enhanced investment
          enhancedInvestment.avgPurchasePrice = investmentValue / totalQuantity;
          enhancedInvestment.totalQuantity = totalQuantity;
        }
      }

      return enhancedInvestment;
    });

    // Calculate return and percentage
    const totalReturn = totalCurrentValue - totalInvestment;
    const returnPercentage = totalInvestment > 0 ? (totalReturn / totalInvestment) * 100 : 0;

    // Group investments by type
    const investmentsByType = enhancedInvestments.reduce((acc: any[], investment) => {
      if (investment.purpose === 'OWNED') {
        // Only process investments that have either totalQuantity or quantity
        if (investment.totalQuantity || investment.quantity) {
          const existingType = acc.find((item) => item.name === investment.type);

          // Calculate the value based on current price for stocks, or average purchase price for others
          let value = 0;

          if (investment.type === 'STOCK' && investment.currentPrice) {
            // Use totalQuantity if available (from purchases array), otherwise fall back to quantity
            const quantity = investment.totalQuantity || investment.quantity;
            value = investment.currentPrice * quantity;
          } else if (investment.avgPurchasePrice && investment.totalQuantity) {
            // Use calculated average price and total quantity from purchases
            value = investment.avgPurchasePrice * investment.totalQuantity;
          } else if (investment.purchasedPrice && investment.quantity) {
            // Fall back to legacy fields
            value = investment.purchasedPrice * investment.quantity;
          }

          if (existingType) {
            existingType.value += value;
          } else {
            acc.push({
              name: investment.type,
              value: value,
            });
          }
        }
      }
      return acc;
    }, []);

    // Sort investments by creation date (newest first)
    enhancedInvestments.sort(
      (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
    );

    // Create performance data (could be expanded in the future)
    const performanceData = enhancedInvestments
      .filter(
        (stock) =>
          stock.purpose === 'OWNED' &&
          stock.type === 'STOCK' &&
          (stock.totalQuantity || stock.quantity),
      )
      .map((stock) => {
        return {
          name: stock.name,
          value: stock.totalProfit || 0,
        };
      });

    return res.status(200).json({
      success: true,
      data: {
        totalInvestment,
        totalCurrentValue,
        totalReturn,
        returnPercentage,
        investmentsByType,
        recentInvestments: enhancedInvestments,
        performanceData,
      },
    });
  } catch (error) {
    console.error('Error fetching investment stats:', error);
    return res.status(500).json({
      success: false,
      error: 'Server Error',
    });
  }
};

export const getPriceTracking = async (req: Request, res: Response): Promise<Response> => {
  const type = req.query.type as string;

  const strategy = typeStrategy[type];

  if (!strategy) {
    return res.status(400).json({
      error: "Invalid type. Use 'mutual', 'stock', 'gold', 'silver', or 'currency'",
    });
  }

  try {
    if (strategy.isScraper) {
      const scrapedData = await strategy.fetchData();
      return res.status(200).json(scrapedData);
    }

    const { buildUrl, formatData } = strategy as DataStrategy<any, StockData>;

    const combinedInvesMent: Record<string, string> = {};

    const investments = await Investments.find({
      userId: req.user.id,
      type: type.toUpperCase(),
      // purpose: 'OWNED',
    });

    investments.forEach((stock) => {
      combinedInvesMent[stock.name] = stock.symbol;
    });

    const allStockResponses = await fetchStockData(combinedInvesMent, buildUrl, formatData);
    return res.status(200).json(allStockResponses);
  } catch (error) {
    console.error(`[${type.toUpperCase()}] Fetch error:`, error);
    return res.status(500).json({ error: 'Failed to fetch price data' });
  }
};
